import { NextFunction, Request, Response } from "express";
import { poolPromise } from "../config/db";
import sql from "mssql";
import { Alert } from "../models/alertsModel";

const schema = process.env.CLIENT_NAME || "2WayAnalytics"
const media = process.env.AZURE_CONTAINER_NAME
const storage = process.env.AZURE_STORAGE_ACCOUNT_NAME
const table = `[${schema}].[Alerts]`;

// CREATE
export const createAlert = async (req: Request, res: Response) => {
  try {
    const alert: Alert = req.body;
    const pool = await poolPromise;

    const result = await pool.request()
      .input("Client", sql.NVarChar, schema)
      .input("Channel", sql.NVarChar, alert.channel)
      .input("PublicationID", sql.NVarChar, alert.publicationId)
      .input("EntityID", sql.Int, alert.entityId)
      .input("AttributeID", sql.Int, alert.attributeId)
      .input("Duration", sql.SmallInt, alert.duration)
      .input("Repeat", sql.Bit, alert.repeat)
      .input("StartDate", sql.DateTime, alert.startDate)
      .input("EndDate", sql.DateTime, alert.endDate)
      .input("MetricType", sql.NVarChar, alert.metricType)
      .input("Metrics", sql.NVarChar, alert.metrics)
      .input("Threshold", sql.Int, alert.threshold)
      .input("Above", sql.Bit, alert.above)
      .input("#Comments", sql.Int, alert.comments || 0)
      .input("Mention", sql.NVarChar, alert.mention)
      .execute("sp_CreateAlert");

    res.status(201).json({ message: "Alert created successfully", result });
  } catch (err) {
    console.error("Create Alert Error:", err);
    res.status(500).json({ error: "Internal Server Error" });
  }
};




// GET ALL ALERTS
export const getAllAlerts = async (req: Request, res: Response) => {
  try {
    const { filter, beginDate, endDate } = req.query;
    
    const pool = await poolPromise;
    const result = await pool.request()
      .input('Client', sql.NVarChar, schema)
      .input('StorageA', sql.NVarChar, storage)
      .input('MContainer', sql.NVarChar, media)
      .input('Filter', sql.NVarChar, filter || 'All') 
      .input('Begin', sql.Date, beginDate || null)    
      .input('End', sql.Date, endDate || null)      
      .execute('sp_App_GetAlerts');
    
    res.json(result.recordset);
  } catch (err) {
    console.error("Get Alerts Error:", err);
    res.status(500).json({ error: "Internal Server Error" });
  }
};
// GET ALERT BY ID
export const getAlertById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      res.status(400).json({ error: 'Invalid alert ID' });
      return;
    }

    const client = req.params.client || '2WayAnalytics'; // Default client if not provided
    const clone = req.query.clone === '1' ? 1 : 0; // Default to 0 if not provided or invalid

    const pool = await poolPromise;

    const result = await pool
      .request()
      .input('Client', sql.NVarChar, client)
      .input('Id', sql.Int, id)
      .input('Clone', sql.Bit, clone)
      .execute('sp_App_GetAlertById');

    if (result.recordset.length === 0) {
      res.status(404).json({ error: 'Alert not found' });
      return;
    }

    res.json(result.recordset[0]);
  } catch (err) {
    console.error('Get Alert by ID Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};
// DEACTIVATE ALERT BY ID
export const deactivateAlertById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.body.id);

    if (isNaN(id)) {
      res.status(400).json({ error: 'Invalid alert ID' });
      return;
    }

    const pool = await poolPromise;

    const result = await pool
      .request()
      .input('Client', sql.NVarChar, schema) // Ensure 'schema' is defined
      .input('Id', sql.Int, id)
      .execute('sp_App_DeactivateAlertById');

    res.json({
      success: true,
      message: `Alert ID ${id} deactivated`,
      returnCode: result.returnValue,
    });
  } catch (err) {
    console.error('Deactivate Alert Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// ACTIVATE ALERT BY ID
export const activateAlertById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      res.status(400).json({ error: 'Invalid alert ID' });
      return;
    }

    const pool = await poolPromise;

    const result = await pool
      .request()
      .input('Client', sql.NVarChar, schema) // Ensure 'schema' is defined
      .input('Id', sql.Int, id)
      .execute('sp_App_ActivateAlertById');

    res.json({
      success: true,
      message: `Alert ID ${id} activated`,
      returnCode: result.returnValue,
    });
  } catch (err) {
    console.error('Activate Alert Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};


// UPDATE
export const updateAlert = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const alert: Alert = req.body;
    const pool = await poolPromise;

    await pool.request()
      .input("id", sql.Int, id)
      .input("Status", sql.NVarChar, alert.status) // Changed from VarChar to NVarChar
      .input("AlertToSend", sql.Bit, alert.alertToSend)
      .input("AlertSent", sql.Bit, alert.alertSent)
      .input("Validated", sql.Bit, alert.validated)
      .query(`UPDATE ${table} SET 
                Status = @Status,
                [Alert to send] = @AlertToSend,
                [Alert sent] = @AlertSent,
                Validated = @Validated
             WHERE id = @id`);

    res.json({ message: "Alert updated successfully" });
  } catch (err) {
    console.error("Update Alert Error:", err);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// CLONE ALERT
export const cloneAlert = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      res.status(400).json({ error: 'Invalid alert ID' });
      return;
    }

    const client = req.params.client || '2WayAnalytics'; 
    const clone = 1;

    const pool = await poolPromise;

    // Fetch alert data for cloning
    const result = await pool
      .request()
      .input('Client', sql.NVarChar, client)
      .input('Id', sql.Int, id)
      .input('Clone', sql.Bit, clone)
      .execute('sp_App_GetAlertById');

    if (result.recordset.length === 0) {
      res.status(404).json({ error: 'Alert not found' });
      return;
    }

    const clonedAlert = result.recordset[0];

    // Return the cloned alert data to populate the edit page
    res.json(clonedAlert);
  } catch (err) {
    console.error('Clone Alert Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// DELETE
export const deleteAlert = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const pool = await poolPromise;

    await pool.request()
      .input("id", sql.Int, id)
      .query(`DELETE FROM ${table} WHERE id = @id`);

    res.json({ message: "Alert deleted successfully" });
  } catch (err) {
    console.error("Delete Alert Error:", err);
    res.status(500).json({ error: "Internal Server Error" });
  }
};
