{"name": "twowayanalytics-backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "ts-node-dev src/index.ts", "start": "node dist/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/mssql": "^9.1.7", "@types/multer": "^1.4.12", "@types/node": "^22.13.4", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}, "dependencies": {"@azure/msal-node": "^3.4.1", "@azure/storage-blob": "^12.26.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}}