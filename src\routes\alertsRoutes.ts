import { Router } from "express";
import {
  create<PERSON><PERSON>t,
  getAll<PERSON><PERSON><PERSON>,
  getAlertById,
  update<PERSON><PERSON>t,
  deleteAlert,
  activateAlertById,
  deactivate<PERSON>lertById,
  cloneAlert,
} from "../controllers/alertsController";

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Alerts
 *   description: Alert management endpoints
 */

/**
 * @swagger
 * /alert:
 *   post:
 *     summary: Create a new alert
 *     tags: [Alerts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Alert'
 *     responses:
 *       201:
 *         description: <PERSON><PERSON> created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AlertResponse'
 *       500:
 *         description: Internal server error
 */
router.post("/alert", createAlert);

/**
 * @swagger
 * /alerts:
 *   get:
 *     summary: Get all alerts
 *     tags: [Alerts]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *           default: All
 *         description: Filter type for alerts
 *       - in: query
 *         name: beginDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering
 *     responses:
 *       200:
 *         description: List of alerts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Alert'
 *       500:
 *         description: Internal server error
 */
router.get("/alerts", getAllAlerts);

/**
 * @swagger
 * /alert/{id}:
 *   get:
 *     summary: Get an alert by ID
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the alert to get
 *       - in: query
 *         name: clone
 *         schema:
 *           type: string
 *           enum: ['0', '1']
 *           default: '0'
 *         description: Flag to clone the alert (1 for cloning, 0 for display)
 *     responses:
 *       200:
 *         description: Alert data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Alert'
 *       400:
 *         description: Invalid ID supplied
 *       404:
 *         description: Alert not found
 *       500:
 *         description: Internal server error
 */
router.get("/alert/:id", getAlertById);

/**
 * @swagger
 * /alert/deactivate:
 *   post:
 *     summary: Deactivate an alert
 *     tags: [Alerts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 description: The alert ID
 *             required:
 *               - id
 *     responses:
 *       200:
 *         description: Alert deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 returnCode:
 *                   type: integer
 *       400:
 *         description: Invalid alert ID
 *       500:
 *         description: Internal server error
 */
router.post('/alert/deactivate', deactivateAlertById);

/**
 * @swagger
 * /alert/activate/{id}:
 *   post:
 *     summary: Activate an alert
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the alert to activate
 *     responses:
 *       200:
 *         description: Alert activated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 returnCode:
 *                   type: integer
 *       400:
 *         description: Invalid alert ID
 *       500:
 *         description: Internal server error
 */
router.post("/alert/activate/:id", activateAlertById);

/**
 * @swagger
 * /alert/{id}:
 *   put:
 *     summary: Update an alert
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the alert to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AlertUpdate'
 *     responses:
 *       200:
 *         description: Alert updated successfully
 *       400:
 *         description: Invalid ID supplied
 *       500:
 *         description: Internal server error
 */
router.put("/alert/:id", updateAlert);

/**
 * @swagger
 * /alert/clone/{id}:
 *   get:
 *     summary: Clone an alert by ID
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the alert to clone
 *     responses:
 *       200:
 *         description: Cloned alert data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Alert'
 *       400:
 *         description: Invalid ID supplied
 *       404:
 *         description: Alert not found
 *       500:
 *         description: Internal server error
 */
router.get("/alert/clone/:id", cloneAlert);

/**
 * @swagger
 * /alert/{id}:
 *   delete:
 *     summary: Delete an alert
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the alert to delete
 *     responses:
 *       200:
 *         description: Alert deleted successfully
 *       400:
 *         description: Invalid ID supplied
 *       500:
 *         description: Internal server error
 */
router.delete("/alert/:id", deleteAlert);

/**
 * @swagger
 * components:
 *   schemas:
 *     Alert:
 *       type: object
 *       properties:
 *         channel:
 *           type: string
 *           description: The channel for the alert
 *           example: "All"
 *         publicationId:
 *           type: string
 *           description: The publication ID
 *           example: "123"
 *         text:
 *           type: string
 *           description: Alert text content
 *         entityId:
 *           type: integer
 *           description: Entity ID
 *           example: 1
 *         attributeId:
 *           type: integer
 *           description: Attribute ID
 *           example: 1
 *         duration:
 *           type: integer
 *           description: Duration in hours
 *           example: 24
 *         repeat:
 *           type: boolean
 *           description: Whether the alert repeats
 *         startDate:
 *           type: string
 *           format: date-time
 *           description: Alert start date
 *         endDate:
 *           type: string
 *           format: date-time
 *           description: Alert end date
 *         metricType:
 *           type: string
 *           description: Type of metric
 *           example: "KPI"
 *         metrics:
 *           type: string
 *           description: Metrics data
 *         threshold:
 *           type: integer
 *           description: Threshold value
 *         above:
 *           type: boolean
 *           description: Whether to alert when above threshold
 *         comments:
 *           type: integer
 *           description: Comment count
 *         mention:
 *           type: string
 *           description: Mention data
 *       required:
 *         - channel
 *         - publicationId
 *         - repeat
 *         - metricType
 *         - above
 * 
 *     AlertUpdate:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           description: Alert status
 *         alertToSend:
 *           type: boolean
 *           description: Whether alert is ready to send
 *         alertSent:
 *           type: boolean
 *           description: Whether alert has been sent
 *         validated:
 *           type: boolean
 *           description: Whether alert has been validated
 * 
 *     AlertResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *           example: "Alert created successfully"
 *         result:
 *           type: object
 *           description: The created alert data
 */

export default router;